# Azure SQL Database Setup Guide - <PERSON><PERSON> Batch Coordinator

## Overview
Complete setup guide for deploying the BBC Tool with Azure SQL Database. Start with the **FREE tier** for development/testing, then upgrade to Basic tier ($5/month) for production.

## 1. Create Azure SQL Database

### Step 1: Create SQL Server
1. Go to [Azure Portal](https://portal.azure.com)
2. Click **Create a resource** → **Databases** → **SQL Database**
3. Configure SQL Server:
   - **Server name**: `cannasol-bbc-sql-server`
   - **Location**: Choose closest region
   - **Authentication**: `Use Azure Active Directory authentication`
   - **Azure AD admin**: Set your admin account

### Step 2: Configure Database
1. **Database name**: `BrezBatchCoordinator`
2. **Apply Free Offer** (RECOMMENDED):
   - Look for: "Want to try Azure SQL Database for free?"
   - Click **"Apply offer"**
   - **Free tier**: 100,000 vCore seconds, 32GB storage, 32GB backup
   - **Cost**: $0/month (perfect for development and testing)
3. **Alternative - Basic Tier** (for production):
   - **Service tier**: `Basic`
   - **DTUs**: `5 DTUs`
   - **Storage**: `2 GB` (included)
   - **Cost**: ~$5/month

### Step 3: Configure Networking
1. **Connectivity method**: `Public endpoint`
2. **Firewall rules**:
   - ✅ Allow Azure services
   - ✅ Add your current IP
   - ✅ Add Azure Static Web App IPs (if needed)

## 2. Execute Database Schema

### Step 1: Connect to Database
1. In Azure Portal, go to your SQL Database
2. Click **Query editor (preview)**
3. Login with Azure AD or SQL authentication

### Step 2: Run Schema Script
```sql
-- Copy and paste the entire contents of docs/azure-sql-simple-schema.sql
-- Execute in Query editor
```

### Step 3: Verify Tables Created
```sql
-- Check all tables were created
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE';

-- Should show: Products, CannasolCapacity, ProductionRequests, 
-- OptimizedSchedules, WeeklyProductionSlots, UserRoles
```

## 3. Configure Azure AD Authentication

### Step 1: Register Application
1. Go to **Azure Active Directory** → **App registrations**
2. Click **New registration**
3. Configure:
   - **Name**: `Brez Batch Coordinator`
   - **Supported account types**: `Single tenant`
   - **Redirect URI**: 
     - `https://green-plant-0ce461e10.2.azurestaticapps.net/auth/callback`
     - `http://localhost:3000/auth/callback` (development)

### Step 2: Configure Authentication
1. **Authentication** → **Platform configurations**
2. Add **Single-page application**:
   - Redirect URIs: Same as above
   - **Access tokens**: ✅ Enabled
   - **ID tokens**: ✅ Enabled

### Step 3: API Permissions
1. **API permissions** → **Add a permission**
2. **Microsoft Graph**:
   - `User.Read` (Delegated)
   - `email` (Delegated)
   - `openid` (Delegated)
   - `profile` (Delegated)

### Step 4: Create Azure AD Groups
Create these groups for role assignment:
- **BBC-Cannasol-Admins**
- **BBC-Cannasol-Operators** 
- **BBC-Cannasol-Viewers**
- **BBC-Brez-Production**

### Step 5: Get Application Details
Copy these for frontend configuration:
- **Application (client) ID**
- **Directory (tenant) ID**

## 4. Configure Database Connection

### Step 1: Get Connection String
1. In SQL Database → **Connection strings**
2. Copy **ADO.NET** connection string
3. Replace `{your_password}` with actual password or use Azure AD

### Step 2: Azure Functions Configuration
Create `local.settings.json`:
```json
{
  "IsEncrypted": false,
  "Values": {
    "AzureWebJobsStorage": "",
    "FUNCTIONS_WORKER_RUNTIME": "python",
    "SQL_CONNECTION_STRING": "Server=tcp:cannasol-bbc-sql-server.database.windows.net,1433;Database=BrezBatchCoordinator;Authentication=Active Directory Default;",
    "AZURE_CLIENT_ID": "your-app-client-id",
    "AZURE_TENANT_ID": "your-tenant-id"
  }
}
```

## 5. Frontend Configuration

### Step 1: Update Environment Variables
Create `.env` file in `/src`:
```env
VITE_AZURE_CLIENT_ID=your-app-client-id
VITE_AZURE_TENANT_ID=your-tenant-id
VITE_API_BASE_URL=https://green-plant-0ce461e10.2.azurestaticapps.net/api
```

### Step 2: Install Required Packages
```bash
cd src
npm install @azure/msal-browser @azure/msal-react
npm uninstall @supabase/supabase-js
```

## 6. Test Database Connection

### Test 1: Query Editor Test
```sql
-- Test basic operations
SELECT * FROM Products;
SELECT * FROM UserRoles;

-- Test insert
INSERT INTO ProductionRequests (ProductId, RequestedByUserId, RequestedBatchSizeL, RequestedQuantityL, RequestedDeliveryDate, MonthlyDeadline)
VALUES (
    (SELECT TOP 1 Id FROM Products),
    'test-user-id',
    500,
    2000,
    '2024-02-15',
    '2024-02-28'
);
```

### Test 2: Connection from Azure Functions
```python
import pyodbc
import os

def test_connection():
    connection_string = os.environ["SQL_CONNECTION_STRING"]
    try:
        conn = pyodbc.connect(connection_string)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM Products")
        result = cursor.fetchone()
        print(f"Products count: {result[0]}")
        return True
    except Exception as e:
        print(f"Connection failed: {e}")
        return False
```

## 7. Security Configuration

### Step 1: Enable Azure AD Authentication
```sql
-- Create Azure AD users in database
CREATE USER [<EMAIL>] FROM EXTERNAL PROVIDER;
ALTER ROLE db_owner ADD MEMBER [<EMAIL>];

-- Create role for application
CREATE USER [Brez Batch Coordinator] FROM EXTERNAL PROVIDER;
ALTER ROLE db_datareader ADD MEMBER [Brez Batch Coordinator];
ALTER ROLE db_datawriter ADD MEMBER [Brez Batch Coordinator];
```

### Step 2: Row Level Security (Optional)
```sql
-- Enable RLS for multi-tenant security
ALTER TABLE ProductionRequests ENABLE ROW LEVEL SECURITY;

-- Create policy for user isolation
CREATE SECURITY POLICY UserIsolationPolicy
ADD FILTER PREDICATE USER_NAME() = RequestedByUserId
ON ProductionRequests;
```

## 8. Monitoring and Maintenance

### Step 1: Enable Monitoring
1. **SQL Database** → **Monitoring**
2. Enable **Query Performance Insight**
3. Set up **Alerts** for DTU usage

### Step 2: Backup Configuration
- **Automatic backups**: Enabled by default
- **Retention**: 7 days (Basic tier)
- **Point-in-time restore**: Available

## 9. Cost Optimization and Scaling

### Development Phase (FREE Tier)
- **vCore seconds**: Monitor usage in Azure portal
- **Storage usage**: 32GB limit (more than enough for BBC Tool)
- **Perfect for**: Development, testing, initial deployment

### Production Scaling Path
```
Start: FREE Tier (Development/Testing, $0/month)
↓
Upgrade: Basic (5 DTUs, $5/month) - Production ready
↓
Scale: Standard S0 (10 DTUs, $15/month) - Higher load
↓
Scale: Standard S1 (20 DTUs, $30/month) - Heavy usage
```

### When to Upgrade from FREE to Basic
- **Ready for production** with real users
- **Need guaranteed performance** (DTUs vs vCore seconds)
- **24/7 availability** requirements
- **SLA requirements** for business operations

## 10. Troubleshooting

### Common Issues
1. **Connection timeout**: Check firewall rules
2. **Authentication failed**: Verify Azure AD configuration
3. **Permission denied**: Check database user roles
4. **DTU limit reached**: Monitor query performance

### Performance Tips
- Use indexes on frequently queried columns
- Avoid complex JOINs (let AI Agents handle complexity)
- Keep queries simple and fast
- Use connection pooling in applications

## Success Criteria
✅ Database created and accessible
✅ Schema deployed successfully
✅ Azure AD authentication working
✅ Sample data inserted
✅ Frontend can connect
✅ Azure Functions can connect
✅ Monitoring enabled

## Next Steps
1. Deploy frontend with Azure AD authentication
2. Create Azure Functions for API endpoints
3. Implement AI Agent integration
4. Test end-to-end workflow
5. Deploy to production

**Total setup time: ~2-3 hours**
**Monthly cost: ~$5 + minimal Azure AD costs**
