# Database Schema Documentation - <PERSON>rez Batch Coordinator

## Overview
This document provides detailed descriptions of each table and field in the BBC Tool database schema. The schema is designed for simple data storage with complex processing handled by AI Agents.

## Architecture Philosophy
- **Simple Storage**: Database stores data, AI Agents handle complex calculations
- **AI-Optimized**: Tables designed for AI Agent consumption and optimization
- **Performance-Focused**: Indexed for common lookup patterns
- **Audit-Ready**: Timestamps and tracking on all records

---

## Table Definitions

### 1. Products Table
**Purpose**: Stores Brez beverage product specifications and requirements.

| Field | Type | Description | Usage |
|-------|------|-------------|-------|
| `Id` | UNIQUEIDENTIFIER | Primary key, auto-generated UUID | Unique identifier for each product |
| `Name` | NVARCHAR(255) | Product display name | "Brez Energy", "Brez Calm", etc. |
| `SKU` | NVARCHAR(100) | Stock Keeping Unit, unique identifier | "BREZ-ENERGY-001" for inventory tracking |
| `ActiveIngredientType` | NVARCHAR(100) | Type of nano-emulsion active ingredient | "Caffeine", "CBD", "Nootropic" - determines processing requirements |
| `TargetPotencyMg` | DECIMAL(10,2) | Target potency in milligrams per unit | 100.0mg caffeine, 25.0mg CBD - affects batch calculations |
| `PreferredBatchSizeL` | INT | Brez's preferred batch size in liters | 500L - what Brez ideally wants to produce |
| `ShelfLifeDays` | INT | Product shelf life in days | 365 days - affects scheduling urgency |
| `CreatedAt` | DATETIME2 | Record creation timestamp | Audit trail and data tracking |
| `UpdatedAt` | DATETIME2 | Last modification timestamp | Updated via trigger on any changes |

**AI Agent Usage**: AI analyzes preferred batch sizes vs Cannasol capacity to optimize production.

---

### 2. CannasolCapacity Table
**Purpose**: Defines Cannasol's equipment capabilities and optimal operating parameters.

| Field | Type | Description | Usage |
|-------|------|-------------|-------|
| `Id` | UNIQUEIDENTIFIER | Primary key, auto-generated UUID | Unique identifier for each equipment |
| `EquipmentName` | NVARCHAR(255) | Human-readable equipment name | "Ultrasonic Processor A", "Nano-Emulsion Tank C" |
| `MaxBatchSizeL` | INT | Maximum batch size equipment can handle | 1000L - hard constraint for AI optimization |
| `MinBatchSizeL` | INT | Minimum efficient batch size | 100L - below this is inefficient |
| `OptimalBatchSizeL` | INT | Cannasol's optimal batch size for efficiency | 750L - AI targets this when possible |
| `ProcessingTimeHours` | DECIMAL(5,2) | Time required to process optimal batch | 4.0 hours - used for scheduling calculations |
| `AvailableHoursPerWeek` | INT | Equipment availability per week | 40 hours - capacity planning constraint |
| `Status` | NVARCHAR(50) | Current equipment status | "Available", "Maintenance", "Offline" |
| `CreatedAt` | DATETIME2 | Record creation timestamp | Equipment tracking and audit |

**AI Agent Usage**: Core data for batch optimization - AI balances Brez requests against these constraints.

---

### 3. ProductionRequests Table
**Purpose**: Stores what Brez wants to produce - the input for AI optimization.

| Field | Type | Description | Usage |
|-------|------|-------------|-------|
| `Id` | UNIQUEIDENTIFIER | Primary key, auto-generated UUID | Unique identifier for each request |
| `ProductId` | UNIQUEIDENTIFIER | Foreign key to Products table | Links request to specific Brez product |
| `RequestedByUserId` | NVARCHAR(450) | Azure AD user ID who made request | Tracks which Brez team member submitted |
| `RequestedBatchSizeL` | INT | Brez's requested batch size in liters | What Brez thinks they want |
| `RequestedQuantityL` | INT | Total volume needed in liters | 2000L total might need multiple batches |
| `RequestedDeliveryDate` | DATE | When Brez needs the product | Drives scheduling urgency |
| `MonthlyDeadline` | DATE | Monthly fill deadline constraint | Hard deadline for monthly production targets |
| `Priority` | INT | Request priority (1-10 scale) | 10 = urgent, 1 = can wait - AI considers this |
| `Status` | NVARCHAR(50) | Current request status | "Pending", "Approved", "Scheduled", "Completed" |
| `Notes` | NVARCHAR(MAX) | Additional request details | Special requirements or context |
| `CreatedAt` | DATETIME2 | When request was submitted | Request timeline tracking |
| `UpdatedAt` | DATETIME2 | Last status change | Updated when status changes |

**AI Agent Usage**: Primary input for optimization - AI analyzes these against Cannasol capacity.

---

### 4. OptimizedSchedules Table
**Purpose**: Stores AI-generated recommendations and optimizations.

| Field | Type | Description | Usage |
|-------|------|-------------|-------|
| `Id` | UNIQUEIDENTIFIER | Primary key, auto-generated UUID | Unique identifier for each optimization |
| `ProductionRequestId` | UNIQUEIDENTIFIER | Links to original production request | Traces optimization back to request |
| `EquipmentId` | UNIQUEIDENTIFIER | Which equipment AI recommends using | AI's equipment selection |
| `RecommendedBatchSizeL` | INT | AI-optimized batch size | May differ from requested size |
| `RecommendedStartDate` | DATE | AI-recommended start date | Optimized for efficiency and deadlines |
| `RecommendedEndDate` | DATE | AI-calculated completion date | Based on processing time + batch size |
| `EstimatedEfficiency` | DECIMAL(5,2) | AI-calculated efficiency percentage | 95.5% = very efficient use of resources |
| `DiscrepancyNotes` | NVARCHAR(MAX) | AI-generated analysis of conflicts | "Requested 500L but 750L more efficient" |
| `OptimizationReason` | NVARCHAR(MAX) | Why AI chose this configuration | Explanation of AI decision-making |
| `Status` | NVARCHAR(50) | Recommendation status | "Proposed", "Accepted", "Rejected" |
| `CreatedByAI` | BIT | Flag indicating AI-generated record | Always true for AI recommendations |
| `CreatedAt` | DATETIME2 | When AI generated recommendation | AI processing timestamp |

**AI Agent Usage**: Output of AI optimization - shows AI's reasoning and recommendations.

---

### 5. WeeklyProductionSlots Table
**Purpose**: Actual scheduled production slots - the final production calendar.

| Field | Type | Description | Usage |
|-------|------|-------------|-------|
| `Id` | UNIQUEIDENTIFIER | Primary key, auto-generated UUID | Unique identifier for each slot |
| `OptimizedScheduleId` | UNIQUEIDENTIFIER | Links to AI recommendation | Connects slot to optimization |
| `WeekStartDate` | DATE | Start of production week | Week of 2024-02-05 for weekly planning |
| `ScheduledStartTime` | DATETIME2 | Exact start time for production | 2024-02-06 08:00:00 - precise scheduling |
| `ScheduledEndTime` | DATETIME2 | Planned completion time | 2024-02-06 16:00:00 - 8-hour production |
| `ActualStartTime` | DATETIME2 | When production actually started | NULL until production begins |
| `ActualEndTime` | DATETIME2 | When production actually completed | NULL until production finishes |
| `ActualBatchSizeL` | INT | Actual volume produced | May differ from planned due to issues |
| `Status` | NVARCHAR(50) | Current production status | "Scheduled", "InProgress", "Completed", "Delayed" |
| `OperatorNotes` | NVARCHAR(MAX) | Cannasol operator notes | Issues, observations, quality notes |
| `CreatedAt` | DATETIME2 | When slot was scheduled | Scheduling timestamp |

**AI Agent Usage**: AI monitors actual vs planned for future optimization improvements.

---

### 6. UserRoles Table
**Purpose**: Defines user roles and their permissions in the system.

| Field | Type | Description | Usage |
|-------|------|-------------|-------|
| `RoleName` | NVARCHAR(50) | Primary key, role identifier | "CannasolAdmin", "BrezProductionTeam" |
| `Organization` | NVARCHAR(20) | Which organization role belongs to | "Cannasol" or "Brez" |
| `Description` | NVARCHAR(255) | Human-readable role description | "Full system access, user management" |
| `Permissions` | NVARCHAR(MAX) | JSON string of permissions | {"batches": ["create", "read", "update"]} |

**Predefined Roles**:
- **CannasolAdmin**: Full system access, user management, system configuration
- **CannasolOperator**: Production execution, data entry, monitoring
- **CannasolViewer**: Read-only access to all data and reports
- **BrezProductionTeam**: Request production, view schedules, access analytics

---

## Relationships and Data Flow

### 1. Request → Optimization Flow
```
ProductionRequests → OptimizedSchedules → WeeklyProductionSlots
```
1. Brez submits ProductionRequest
2. AI Agent creates OptimizedSchedule with recommendations
3. Approved optimizations become WeeklyProductionSlots

### 2. Product → Capacity Matching
```
Products.PreferredBatchSizeL ↔ CannasolCapacity.OptimalBatchSizeL
```
AI analyzes mismatches and suggests optimal compromises.

### 3. Timeline Constraints
```
ProductionRequests.MonthlyDeadline → OptimizedSchedules.RecommendedStartDate
```
AI ensures all recommendations meet deadline constraints.

---

## Indexes for Performance

### Query Optimization
- **ProductionRequests.Status** - Filter by pending/approved requests
- **ProductionRequests.RequestedDeliveryDate** - Sort by urgency
- **WeeklyProductionSlots.WeekStartDate** - Weekly calendar views
- **OptimizedSchedules.Status** - Filter by recommendation status

### AI Agent Optimization
- **Products.ActiveIngredientType** - Group by ingredient type
- **CannasolCapacity.Status** - Find available equipment
- **ProductionRequests.Priority** - Sort by business priority

---

## AI Agent Integration Points

### Input Data for AI
- **ProductionRequests**: What Brez wants
- **CannasolCapacity**: What Cannasol can do
- **Products**: Product specifications and constraints

### AI Output Data
- **OptimizedSchedules**: AI recommendations and reasoning
- **DiscrepancyNotes**: Conflict analysis and suggestions
- **EstimatedEfficiency**: Performance predictions

### Feedback Loop
- **WeeklyProductionSlots.Actual*** fields provide real-world data
- AI learns from actual vs predicted performance
- Improves future optimization accuracy

---

## Business Logic Examples

### Batch Size Optimization
```
IF ProductionRequests.RequestedBatchSizeL < CannasolCapacity.MinBatchSizeL
THEN AI suggests combining multiple requests
ELSE IF RequestedBatchSizeL > MaxBatchSizeL  
THEN AI suggests splitting into multiple batches
ELSE AI optimizes toward OptimalBatchSizeL
```

### Deadline Management
```
IF ProductionRequests.RequestedDeliveryDate < (TODAY + ProcessingTimeHours)
THEN AI flags as "URGENT" and prioritizes
ELSE AI optimizes for efficiency within deadline
```

### Equipment Selection
```
AI selects equipment WHERE:
- Status = 'Available'
- MaxBatchSizeL >= RecommendedBatchSizeL
- AvailableHoursPerWeek sufficient for timeline
```

This schema provides the foundation for intelligent batch coordination between Brez's needs and Cannasol's capabilities! 🎯
