# <PERSON><PERSON> Batch Coordinator - Implementation Plan

## Project Overview

**Client:** <PERSON><PERSON> (beverage company using nano-emulsified actives)  
**Service Provider:** Cannasol (ultrasonic liquid processing services)  
**Purpose:** Web application for scheduling and optimizing batch production of nano-emulsions  
**Key Features:** Real-time batch size optimization, weekly production slot allocation, monthly fill deadline tracking, discrepancy highlighting, and efficiency recommendations

## Technology Stack

- **Frontend:** React (JavaScript)
- **Hosting:** Azure Static Web App with GitHub CI/CD
- **Backend:** Azure Serverless Functions (Python)
- **Database:** Supabase

---

## ✅ Phase 1: Project Setup & Infrastructure - COMPLETED

### 1.1 Repository & Development Environment
- [x] Create GitHub repository "brez-batch-coordinator" with README.md containing project overview and tech stack
- [x] Initialize frontend React application using Create React App in `/src` directory
- [x] Create `/api` directory for Azure Functions with Python requirements.txt
- [x] Configure .gitignore for React, Python, and Azure Functions
- [x] Create project structure documentation in `/docs/architecture.md`

### 1.2 Azure Infrastructure Setup
- [x] Create Azure Static Web App resource in Azure Portal
- [x] Configure GitHub Actions workflow for CI/CD deployment (.github/workflows/azure-static-web-apps.yml)
- [x] Create Azure Function App resource for serverless backend
- [x] Configure Azure Function App settings and environment variables
- [x] Set up local.settings.json template for development environment

### 1.3 Supabase Configuration
- [x] Create Supabase project and obtain connection credentials
- [x] Document database connection strings in secure environment configuration
- [x] Create initial database connection test script in `/api/utils/db_connection.py`
- [x] Configure Supabase client libraries for both frontend and backend
- [x] Create environment configuration documentation

---

## Phase 2: Database Schema & Authentication

### 2.1 Core Database Schema
- [x] Create `/docs/azure-sql-simple-schema.sql` with simplified database schema
- [x] Create `/docs/azure-sql-database-setup.md` with complete setup guide
- [ ] Create Azure SQL Database Basic tier ($5/month)
- [ ] Execute database schema in Azure SQL Database
- [ ] Configure Azure AD authentication integration
  - [ ] Register BBC application in Azure AD
  - [ ] Configure authentication for Cannasol employees (@cannasolusa.com)
  - [ ] Set up manual user management for Brez employees
  - [ ] Create Azure AD groups for role assignment
- [ ] Set up database security
  - [ ] Configure Azure AD database authentication
  - [ ] Create database users and roles
  - [ ] Implement basic access control
- [ ] Test database connections from frontend and Azure Functions

### 2.2 Authentication & Authorization System
- [ ] Design multi-organization user management system
  - [ ] Create user roles:
    - [ ] Cannasol Admin (full system access, user management, system configuration)
    - [ ] Cannasol Operator (batch execution, data entry, production monitoring)
    - [ ] Cannasol Viewer (read-only access to all data and reports)
    - [ ] Brez Production Team Member (batch planning, scheduling, analytics access)
  - [ ] Define organization types: Cannasol (internal), Brez (client)
  - [ ] Plan permission matrix for cross-organization access
- [ ] Implement Azure AD/Entra ID integration
  - [ ] Register BBC app in Azure AD tenant (@cannasolusa.com domain)
  - [ ] Configure OIDC/OAuth2 settings for Cannasol employees
  - [ ] Set up automatic role assignment based on AD groups:
    - [ ] Map AD group "BBC-Admins" → cannasol_admin role
    - [ ] Map AD group "BBC-Operators" → cannasol_operator role
    - [ ] Map AD group "BBC-Viewers" → cannasol_viewer role
  - [ ] Enforce @cannasolusa.com domain restriction for Cannasol roles
  - [ ] Test Azure AD login flow with domain validation
- [ ] Set up Brez employee authentication
  - [ ] Create manual user management system in Azure AD or database
  - [ ] Implement user invitation system for Brez employees
  - [ ] Set up role assignment for Brez users
  - [ ] Configure access controls for non-domain users
- [ ] Frontend authentication integration
  - [ ] Implement Azure AD login button and flow
  - [ ] Create Brez employee login/registration forms
  - [ ] Add role-based navigation and feature access
  - [ ] Implement session management and token refresh

### 2.3 Database Tables & Models
- [ ] Create SQL migration script for `companies` table (id, name, type, contact_info, created_at)
- [ ] Create SQL migration script for `products` table (id, company_id, name, active_ingredient, concentration, created_at)
- [ ] Create SQL migration script for `batch_configurations` table (id, min_size, max_size, optimal_size, processing_time_minutes)
- [ ] Create SQL migration script for `production_schedules` table (id, week_start, week_end, monthly_deadline, status)
- [ ] Create SQL migration script for `production_slots` table (id, schedule_id, slot_datetime, duration_minutes, available_capacity)

### 2.4 Relational Tables
- [ ] Create SQL migration script for `batch_requests` table (id, product_id, requested_quantity, requested_date, status, created_by)
- [ ] Create SQL migration script for `allocated_batches` table (id, request_id, slot_id, allocated_quantity, efficiency_score)
- [ ] Create SQL migration script for `optimization_suggestions` table (id, request_id, suggestion_type, description, potential_savings)
- [ ] Create indexes for foreign keys and frequently queried columns
- [ ] Create database views for common queries (weekly_schedule_view, batch_efficiency_view)

### 2.5 Python ORM Models
- [ ] Create `/api/models/base_model.py` with BaseModel class for common fields and methods
- [ ] Create `/api/models/company.py` with Company class and validation methods
- [ ] Create `/api/models/product.py` with Product class and relationship methods
- [ ] Create `/api/models/batch_configuration.py` with BatchConfiguration class and calculation methods
- [ ] Create `/api/models/production_schedule.py` with ProductionSchedule class and slot management methods
- [ ] Create `/api/models/batch_request.py` with BatchRequest class and status management
- [ ] Create `/api/models/optimization.py` with OptimizationSuggestion class and scoring algorithms

---

## Phase 3: Core Backend Services

### 3.1 Base Service Architecture
- [ ] Create `/api/services/base_service.py` with BaseService class for common CRUD operations
- [ ] Create `/api/utils/validators.py` with input validation decorators
- [ ] Create `/api/utils/error_handlers.py` with custom exception classes
- [ ] Create `/api/utils/response_formatters.py` with standardized API response formats

### 3.2 Business Logic Services
- [ ] Create `/api/services/batch_optimizer.py` with BatchOptimizer class for calculating optimal batch sizes
- [ ] Create `/api/services/schedule_allocator.py` with ScheduleAllocator class for slot assignment logic
- [ ] Create `/api/services/efficiency_analyzer.py` with EfficiencyAnalyzer class for discrepancy detection
- [ ] Create `/api/services/suggestion_engine.py` with SuggestionEngine class for improvement recommendations
- [ ] Create `/api/services/deadline_tracker.py` with DeadlineTracker class for monthly fill deadline monitoring

### 3.3 Azure Functions API Endpoints
- [ ] Create `/api/functions/auth_function.py` with authentication and authorization logic
- [ ] Create `/api/functions/batch_requests_function.py` with CRUD endpoints for batch requests
- [ ] Create `/api/functions/schedule_function.py` with endpoints for viewing and managing schedules
- [ ] Create `/api/functions/optimization_function.py` with endpoints for batch optimization calculations
- [ ] Create `/api/functions/allocation_function.py` with endpoints for slot allocation operations
- [ ] Create `/api/functions/analytics_function.py` with endpoints for efficiency metrics and reports

---

## Phase 4: Frontend Component Architecture

### 4.1 Core UI Framework
- [ ] Create `/src/src/components/layout/AppLayout.jsx` with main application shell
- [ ] Create `/src/src/components/layout/NavigationBar.jsx` with responsive navigation
- [ ] Create `/src/src/components/layout/Sidebar.jsx` with menu structure
- [ ] Create `/src/src/components/common/LoadingSpinner.jsx` for async operations
- [ ] Create `/src/src/components/common/ErrorBoundary.jsx` for error handling
- [ ] Create `/src/src/components/common/NotificationToast.jsx` for user feedback

### 4.2 Authentication Components
- [ ] Create `/src/src/components/auth/LoginForm.jsx` with form validation
- [ ] Create `/src/src/components/auth/ProtectedRoute.jsx` for route protection
- [ ] Create `/src/src/context/AuthContext.jsx` for authentication state management
- [ ] Create `/src/src/hooks/useAuth.js` custom hook for auth operations

### 4.3 Dashboard Components
- [ ] Create `/frontend/src/components/dashboard/DashboardContainer.jsx` as main dashboard wrapper
- [ ] Create `/frontend/src/components/dashboard/WeeklyScheduleView.jsx` for schedule visualization
- [ ] Create `/frontend/src/components/dashboard/BatchEfficiencyChart.jsx` for efficiency metrics
- [ ] Create `/frontend/src/components/dashboard/DeadlineTracker.jsx` for monthly deadline monitoring
- [ ] Create `/frontend/src/components/dashboard/QuickActions.jsx` for common operations

### 4.4 Batch Management Components
- [ ] Create `/frontend/src/components/batches/BatchRequestForm.jsx` with dynamic form fields
- [ ] Create `/frontend/src/components/batches/BatchRequestList.jsx` with sorting and filtering
- [ ] Create `/frontend/src/components/batches/BatchDetailsModal.jsx` for detailed view
- [ ] Create `/frontend/src/components/batches/OptimizationSuggestions.jsx` for displaying recommendations
- [ ] Create `/frontend/src/components/batches/BatchSizeCalculator.jsx` for real-time calculations

### 4.5 Schedule Management Components
- [ ] Create `/frontend/src/components/schedule/WeeklyCalendar.jsx` with drag-and-drop functionality
- [ ] Create `/frontend/src/components/schedule/ProductionSlot.jsx` for individual slot representation
- [ ] Create `/frontend/src/components/schedule/SlotAllocationModal.jsx` for allocation interface
- [ ] Create `/frontend/src/components/schedule/CapacityIndicator.jsx` for visual capacity display
- [ ] Create `/frontend/src/components/schedule/ConflictResolver.jsx` for handling scheduling conflicts

---

## Phase 5: Service Integration

### 5.1 API Client Setup
- [ ] Create `/frontend/src/services/api/apiClient.js` with axios configuration and interceptors
- [ ] Create `/frontend/src/services/api/authService.js` for authentication API calls
- [ ] Create `/frontend/src/services/api/batchService.js` for batch-related API calls
- [ ] Create `/frontend/src/services/api/scheduleService.js` for schedule-related API calls
- [ ] Create `/frontend/src/services/api/optimizationService.js` for optimization API calls

### 5.2 State Management
- [x] Create `/frontend/src/store/store.js` with Redux or Context API setup
- [ ] Create `/frontend/src/store/slices/batchSlice.js` for batch state management
- [ ] Create `/frontend/src/store/slices/scheduleSlice.js` for schedule state management
- [ ] Create `/frontend/src/store/slices/uiSlice.js` for UI state (modals, notifications)
- [ ] Create `/frontend/src/hooks/useOptimization.js` for optimization-related state logic

### 5.3 Real-time Features
- [ ] Implement WebSocket connection in `/frontend/src/services/websocket/socketClient.js`
- [ ] Create `/backend/functions/websocket_function.py` for real-time updates
- [ ] Implement real-time schedule updates in WeeklyScheduleView component
- [ ] Implement real-time optimization recalculations on batch size changes
- [ ] Create notification system for schedule conflicts and deadline alerts

---

## Phase 6: Advanced Features

### 6.1 Optimization Engine
- [ ] Implement batch size optimization algorithm in `/backend/algorithms/batch_optimizer.py`
- [ ] Create machine learning model for demand prediction in `/backend/ml/demand_predictor.py`
- [ ] Implement efficiency scoring system in `/backend/algorithms/efficiency_scorer.py`
- [ ] Create recommendation engine for process improvements
- [ ] Implement automated rescheduling suggestions for conflicts

### 6.2 Reporting & Analytics
- [ ] Create `/frontend/src/components/reports/EfficiencyReport.jsx` with downloadable reports
- [ ] Create `/frontend/src/components/reports/ProductionMetrics.jsx` with KPI dashboards
- [ ] Create `/frontend/src/components/reports/TrendAnalysis.jsx` for historical analysis
- [ ] Implement export functionality for CSV and PDF reports
- [ ] Create scheduled report generation in Azure Functions

### 6.3 User Experience Enhancements
- [ ] Implement keyboard shortcuts for common actions
- [ ] Create guided tour for new users using intro.js
- [ ] Implement user preferences storage and customization
- [ ] Add advanced filtering and search capabilities
- [ ] Create mobile-responsive design optimizations

---

## Phase 7: Testing & Deployment

### 7.1 Testing Infrastructure
- [ ] Create `/frontend/src/tests/setupTests.js` with testing configuration
- [ ] Write unit tests for all service classes in `/backend/tests/unit/`
- [ ] Write integration tests for API endpoints in `/backend/tests/integration/`
- [ ] Create component tests for critical UI components
- [ ] Implement end-to-end tests using Cypress in `/frontend/cypress/`

### 7.2 Performance & Security
- [ ] Implement API rate limiting in Azure Functions
- [ ] Add input sanitization for all user inputs
- [ ] Implement row-level security in Supabase
- [ ] Create performance monitoring with Application Insights
- [ ] Implement caching strategy for frequently accessed data
- [ ] **Evaluate Microsoft Defender for SQL before production deployment**
  - [ ] Assess business requirements for advanced threat protection
  - [ ] Review compliance needs (SOX, HIPAA, industry standards)
  - [ ] Analyze cost vs. security benefit ($15/month vs. risk mitigation)
  - [ ] Consider data sensitivity level (customer data, financial data, etc.)
  - [ ] Make go/no-go decision based on business maturity and budget

### 7.3 Deployment & Documentation
- [ ] Create production environment variables configuration
- [ ] Set up staging environment in Azure
- [ ] Create user documentation in `/docs/user-guide.md`
- [ ] Create API documentation using Swagger/OpenAPI
- [ ] Configure automated backup strategy for Supabase
- [ ] Create runbook for common operational tasks
- [ ] Implement health check endpoints for monitoring
- [ ] Set up alerts for critical system events

---

## Phase 8: Post-Launch Optimization

### 8.1 Monitoring & Maintenance
- [ ] Configure Azure Application Insights dashboards
- [ ] Set up automated error reporting with Sentry
- [ ] Create performance benchmarks and monitoring
- [ ] Implement automated database maintenance jobs
- [ ] Create feedback collection mechanism for users

### 8.2 Continuous Improvement
- [ ] Analyze user behavior and optimize UI/UX
- [ ] Implement A/B testing framework
- [ ] Create feature flag system for gradual rollouts
- [ ] Set up automated performance testing
- [ ] Plan for scalability improvements based on usage patterns

---

## Implementation Notes

### For Each Task:
1. Complete and test before moving the 🚧 flag
2. Commit code with descriptive commit messages
3. Update documentation as features are implemented
4. Run tests after each phase completion
5. Request code review for critical components

### Code Organization Principles:
- **Modular Design:** Each function should have a single responsibility
- **Object-Oriented:** Organize code into well-defined, concise classes
- **Short Functions:** Keep functions focused and under 20 lines when possible
- **Clear Interfaces:** Define clear contracts between modules
- **Dependency Injection:** Use DI for better testability and flexibility

### Version Control Strategy:
- Main branch for production code
- Develop branch for integration
- Feature branches for each phase/module
- Pull requests for all merges
- Semantic versioning for releases

### Documentation Requirements:
- Inline code comments for complex logic
- README files in each major directory
- API documentation with examples
- Architecture decision records (ADRs)
- User guides with screenshots

### Quality Assurance:
- Code coverage target: 80%
- Performance benchmarks defined
- Security review checklist
- Accessibility compliance (WCAG 2.1)
- Cross-browser testing matrix

---

## Project Timeline Estimate

- **Phase 1:** 1 week - Project setup
- **Phase 2:** 1 week - Database design
- **Phase 3:** 2 weeks - Backend services
- **Phase 4:** 2 weeks - Frontend components
- **Phase 5:** 1 week - Integration
- **Phase 6:** 2 weeks - Advanced features
- **Phase 7:** 1 week - Testing & deployment
- **Phase 8:** Ongoing - Post-launch optimization

**Total Initial Development:** ~10 weeks

---

## Risk Mitigation

### Technical Risks:
- **Azure service limits:** Monitor usage and plan for scaling
- **Real-time performance:** Implement efficient caching and queuing
- **Data consistency:** Use transactions and proper locking mechanisms

### Business Risks:
- **Requirement changes:** Maintain flexible architecture
- **User adoption:** Plan comprehensive training materials
- **Integration complexity:** Develop robust error handling

### Mitigation Strategies:
1. Regular stakeholder communication
2. Incremental delivery and feedback loops
3. Comprehensive testing at each phase
4. Performance monitoring from day one
5. Clear rollback procedures

---

## Success Metrics

### Technical Metrics:
- Page load time < 2 seconds
- API response time < 500ms
- 99.9% uptime
- Zero critical security vulnerabilities

### Business Metrics:
- 50% reduction in scheduling time
- 30% improvement in batch efficiency
- 90% user satisfaction score
- ROI achieved within 6 months

### User Experience Metrics:
- Task completion rate > 95%
- Error rate < 2%
- User engagement > 80%
- Support ticket volume < 5% of users

---

*This implementation plan is designed to be executed by an AI coding agent with clear, actionable tasks that follow modular, object-oriented principles with focused, single-responsibility functions.*